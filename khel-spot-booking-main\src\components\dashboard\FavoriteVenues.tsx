import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Star, 
  MapPin, 
  Heart,
  Calendar,
  Eye
} from 'lucide-react';
import { Link } from 'react-router-dom';

interface Venue {
  id: number;
  name: string;
  city: string;
  area: string;
  images: string[];
  average_rating: number;
  price_per_hour: string;
}

interface FavoriteVenue {
  id: number;
  venues: Venue;
}

interface FavoriteVenuesProps {
  venues: FavoriteVenue[];
  showViewAll?: boolean;
}

export const FavoriteVenues = ({ venues, showViewAll }: FavoriteVenuesProps) => {
  if (venues.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Favorite Venues</span>
            {showViewAll && (
              <Button variant="outline" size="sm" asChild>
                <Link to="/dashboard/favorites">View All</Link>
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Heart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 mb-4">No favorite venues yet</p>
            <Button asChild>
              <Link to="/venues">Discover Venues</Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Favorite Venues</span>
          {showViewAll && (
            <Button variant="outline" size="sm" asChild>
              <Link to="/dashboard/favorites">View All</Link>
            </Button>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {venues.map((favorite) => {
            const venue = favorite.venues;
            return (
              <div
                key={favorite.id}
                className="border rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-start space-x-4">
                  {/* Venue Image */}
                  <img
                    src={venue.images?.[0] || '/placeholder.svg'}
                    alt={venue.name}
                    className="w-16 h-16 rounded-lg object-cover flex-shrink-0"
                  />

                  {/* Venue Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div>
                        <h4 className="font-semibold text-gray-900 truncate">
                          {venue.name}
                        </h4>
                        <div className="flex items-center text-sm text-gray-500 mt-1">
                          <MapPin className="h-3 w-3 mr-1" />
                          {venue.area}, {venue.city}
                        </div>
                      </div>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-red-500 hover:text-red-600 hover:bg-red-50"
                      >
                        <Heart className="h-4 w-4 fill-current" />
                      </Button>
                    </div>

                    {/* Rating and Price */}
                    <div className="flex items-center justify-between mt-3">
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center">
                          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                          <span className="text-sm font-medium ml-1">
                            {venue.average_rating || 0}
                          </span>
                        </div>
                        
                        <Badge variant="secondary" className="text-xs">
                          ৳{parseFloat(venue.price_per_hour).toLocaleString()}/hr
                        </Badge>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Button variant="outline" size="sm" asChild>
                          <Link to={`/venues/${venue.id}`}>
                            <Eye className="h-3 w-3 mr-1" />
                            View
                          </Link>
                        </Button>
                        
                        <Button size="sm" className="bg-green-600 hover:bg-green-700" asChild>
                          <Link to={`/venues/${venue.id}?book=true`}>
                            <Calendar className="h-3 w-3 mr-1" />
                            Book
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};
