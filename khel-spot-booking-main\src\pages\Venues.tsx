import { useState, useEffect } from 'react';
import { Search, MapPin, Star, Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { <PERSON>, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AuthModal } from '@/components/AuthModal';
import { BookingModal } from '@/components/BookingModal';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/integrations/supabase/client';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

interface Venue {
  id: number;
  name: string;
  description: string;
  address: string;
  city: string;
  area: string;
  price_per_hour: number;
  currency: string;
  average_rating: number;
  total_reviews: number;
  images: string[];
  amenities: string[];
  surface_type: string;
  capacity: number;
  is_indoor: boolean;
  sports: { name: string; icon: string } | null;
}

const Venues = () => {
  const { user } = useAuth();
  const [venues, setVenues] = useState<Venue[]>([]);
  const [filteredVenues, setFilteredVenues] = useState<Venue[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCity, setSelectedCity] = useState('');
  const [selectedSport, setSelectedSport] = useState('');
  const [priceRange, setPriceRange] = useState('');
  const [loading, setLoading] = useState(true);
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [selectedVenue, setSelectedVenue] = useState<Venue | null>(null);
  const [isBookingModalOpen, setIsBookingModalOpen] = useState(false);

  // Get unique cities and sports for filters
  const cities = [...new Set(venues.map(venue => venue.city))].filter(Boolean);
  const sports = [...new Set(venues.map(venue => venue.sports?.name).filter(Boolean))];

  useEffect(() => {
    fetchVenues();

    // Set up real-time subscription for venues
    const venuesSubscription = supabase
      .channel('venues-realtime')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'venues' },
        (payload) => {
          console.log('Venues updated in real-time:', payload.eventType, payload.new || payload.old);
          // Refetch venues when any venue is updated
          fetchVenues();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(venuesSubscription);
    };
  }, []);

  useEffect(() => {
    filterVenues();
  }, [venues, searchQuery, selectedCity, selectedSport, priceRange]);

  const fetchVenues = async () => {
    try {
      const { data, error } = await supabase
        .from('venues')
        .select(`
          *,
          sports:sport_id (
            name,
            icon
          )
        `)
        .eq('is_active', true)
        .order('average_rating', { ascending: false });

      if (error) throw error;
      
      // Transform the data to match our Venue interface
      const transformedVenues = (data || []).map((venue: any) => ({
        ...venue,
        images: Array.isArray(venue.images) ? venue.images : 
                venue.images ? [venue.images] : ["/placeholder.svg"],
        amenities: Array.isArray(venue.amenities) ? venue.amenities : 
                   venue.amenities ? [venue.amenities] : []
      }));
      
      setVenues(transformedVenues);
    } catch (error) {
      console.error('Error fetching venues:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterVenues = () => {
    let filtered = venues;

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(venue =>
        venue.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        venue.area.toLowerCase().includes(searchQuery.toLowerCase()) ||
        venue.city.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // City filter
    if (selectedCity) {
      filtered = filtered.filter(venue => venue.city === selectedCity);
    }

    // Sport filter
    if (selectedSport) {
      filtered = filtered.filter(venue => venue.sports?.name === selectedSport);
    }

    // Price range filter
    if (priceRange) {
      const [min, max] = priceRange.split('-').map(Number);
      filtered = filtered.filter(venue => {
        if (max) {
          return venue.price_per_hour >= min && venue.price_per_hour <= max;
        } else {
          return venue.price_per_hour >= min;
        }
      });
    }

    setFilteredVenues(filtered);
  };

  const handleBookNow = (venue: Venue) => {
    if (!user) {
      setIsAuthModalOpen(true);
      return;
    }
    setSelectedVenue(venue);
    setIsBookingModalOpen(true);
  };

  const clearFilters = () => {
    setSearchQuery('');
    setSelectedCity('');
    setSelectedSport('');
    setPriceRange('');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading venues...</p>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      {/* Hero Section with Search */}
      <section className="bg-green-600 text-white py-16">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold mb-4">Find Your Perfect Venue</h1>
            <p className="text-xl text-green-100">
              Discover and book the best sports venues in Bangladesh
            </p>
          </div>

          {/* Search Bar */}
          <div className="max-w-2xl mx-auto bg-white rounded-full p-2 shadow-lg">
            <div className="flex items-center gap-2">
              <div className="flex-1 flex items-center gap-3 px-4">
                <Search className="text-gray-400 h-5 w-5" />
                <Input
                  placeholder="Search by venue name, area, or city..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="border-0 text-gray-800 placeholder-gray-500 focus-visible:ring-0"
                />
              </div>
              <Button size="lg" className="bg-green-600 hover:bg-green-700 rounded-full px-8">
                Search
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Filters */}
      <section className="py-6 bg-white border-b">
        <div className="max-w-6xl mx-auto px-4">
          <div className="flex flex-wrap gap-4 items-center justify-between">
            <div className="flex flex-wrap gap-4">
              <Select value={selectedCity} onValueChange={setSelectedCity}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select City" />
                </SelectTrigger>
                <SelectContent>
                  {cities.map((city) => (
                    <SelectItem key={city} value={city}>{city}</SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={selectedSport} onValueChange={setSelectedSport}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select Sport" />
                </SelectTrigger>
                <SelectContent>
                  {sports.map((sport) => (
                    <SelectItem key={sport} value={sport}>{sport}</SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={priceRange} onValueChange={setPriceRange}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Price Range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0-1000">৳0 - ৳1,000</SelectItem>
                  <SelectItem value="1000-2000">৳1,000 - ৳2,000</SelectItem>
                  <SelectItem value="2000-3000">৳2,000 - ৳3,000</SelectItem>
                  <SelectItem value="3000">৳3,000+</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={clearFilters}>
                Clear Filters
              </Button>
              <span className="text-sm text-gray-600">
                {filteredVenues.length} venue{filteredVenues.length !== 1 ? 's' : ''} found
              </span>
            </div>
          </div>
        </div>
      </section>

      {/* Venues Grid */}
      <section className="py-12">
        <div className="max-w-6xl mx-auto px-4">
          {filteredVenues.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-600 text-lg">No venues found matching your criteria.</p>
              <Button variant="outline" onClick={clearFilters} className="mt-4">
                Clear Filters
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {filteredVenues.map((venue) => (
                <Card key={venue.id} className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-2 cursor-pointer border-0 shadow-lg">
                  <CardHeader className="p-0">
                    <div className="relative overflow-hidden rounded-t-lg">
                      <img
                        src={venue.images?.[0] || "/placeholder.svg"}
                        alt={venue.name}
                        className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300"
                      />
                      <div className="absolute top-3 right-3 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                        {venue.is_indoor ? 'Indoor' : 'Outdoor'}
                      </div>
                      {venue.sports && (
                        <div className="absolute top-3 left-3 bg-white/90 px-2 py-1 rounded-full text-xs font-medium">
                          {venue.sports.name}
                        </div>
                      )}
                    </div>
                  </CardHeader>
                  
                  <CardContent className="p-4">
                    <h3 className="font-bold text-lg mb-2 text-gray-800 group-hover:text-green-600 transition-colors">
                      {venue.name}
                    </h3>
                    
                    <div className="flex items-center gap-1 text-gray-600 mb-2">
                      <MapPin className="h-4 w-4" />
                      <span className="text-sm">{venue.area}, {venue.city}</span>
                    </div>

                    <div className="flex items-center gap-1 mb-3">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span className="font-medium text-sm">{venue.average_rating || 0}</span>
                      <span className="text-gray-500 text-sm">({venue.total_reviews || 0} reviews)</span>
                    </div>

                    <div className="flex flex-wrap gap-1 mb-3">
                      {venue.amenities?.slice(0, 2).map((amenity, index) => (
                        <span key={index} className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                          {amenity}
                        </span>
                      ))}
                    </div>

                    <div className="text-sm text-gray-600 mb-2">
                      <span>Capacity: {venue.capacity || 'N/A'} players</span>
                    </div>
                    {venue.surface_type && (
                      <div className="text-sm text-gray-600">
                        <span>Surface: {venue.surface_type}</span>
                      </div>
                    )}
                  </CardContent>

                  <CardFooter className="p-4 pt-0 flex items-center justify-between">
                    <div>
                      <span className="text-xl font-bold text-green-600">
                        ৳{venue.price_per_hour}
                      </span>
                      <span className="text-gray-500 text-sm">/hour</span>
                    </div>
                    <Button 
                      size="sm" 
                      className="bg-green-600 hover:bg-green-700"
                      onClick={() => handleBookNow(venue)}
                    >
                      Book Now
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </div>
      </section>

      <Footer />

      {/* Modals */}
      <AuthModal 
        isOpen={isAuthModalOpen} 
        onClose={() => setIsAuthModalOpen(false)}
        onSuccess={() => setIsAuthModalOpen(false)}
      />

      <BookingModal
        isOpen={isBookingModalOpen}
        onClose={() => setIsBookingModalOpen(false)}
        venue={selectedVenue}
        onBookingSuccess={() => {
          setIsBookingModalOpen(false);
          // Could redirect to bookings page or show success message
        }}
      />
    </div>
  );
};

export default Venues;
