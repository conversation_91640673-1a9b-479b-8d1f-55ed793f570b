import { useLocation, Link } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/button';
import { 
  Home, 
  Calendar, 
  MapPin, 
  Star, 
  CreditCard, 
  User, 
  Bell, 
  HelpCircle, 
  LogOut,
  X,
  MessageSquare,
  Clock,
  Settings
} from 'lucide-react';

interface DashboardSidebarProps {
  onClose?: () => void;
}

export const DashboardSidebar = ({ onClose }: DashboardSidebarProps) => {
  const location = useLocation();
  const { signOut } = useAuth();

  const navigation = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: Home,
      current: location.pathname === '/dashboard'
    },
    {
      name: 'My Bookings',
      href: '/dashboard/bookings',
      icon: Calendar,
      current: location.pathname.startsWith('/dashboard/bookings')
    },
    {
      name: 'Browse Venues',
      href: '/venues',
      icon: MapPin,
      current: location.pathname === '/venues'
    },
    {
      name: 'Favorites',
      href: '/dashboard/favorites',
      icon: Star,
      current: location.pathname === '/dashboard/favorites'
    },
    {
      name: 'Payment History',
      href: '/dashboard/payments',
      icon: CreditCard,
      current: location.pathname.startsWith('/dashboard/payments')
    },
    {
      name: 'My Reviews',
      href: '/dashboard/reviews',
      icon: MessageSquare,
      current: location.pathname === '/dashboard/reviews'
    },
    {
      name: 'Recent Activity',
      href: '/dashboard/activity',
      icon: Clock,
      current: location.pathname === '/dashboard/activity'
    }
  ];

  const secondaryNavigation = [
    {
      name: 'Profile Settings',
      href: '/dashboard/profile',
      icon: User,
      current: location.pathname === '/dashboard/profile'
    },
    {
      name: 'Notifications',
      href: '/dashboard/notifications',
      icon: Bell,
      current: location.pathname === '/dashboard/notifications'
    },
    {
      name: 'Settings',
      href: '/dashboard/settings',
      icon: Settings,
      current: location.pathname === '/dashboard/settings'
    },
    {
      name: 'Help & Support',
      href: '/dashboard/support',
      icon: HelpCircle,
      current: location.pathname === '/dashboard/support'
    }
  ];

  const handleSignOut = async () => {
    await signOut();
    if (onClose) onClose();
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b">
        <Link to="/" className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">KT</span>
          </div>
          <span className="text-xl font-bold text-gray-900">KhelteThako</span>
        </Link>
        
        {/* Close button for mobile */}
        {onClose && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="lg:hidden"
          >
            <X className="h-5 w-5" />
          </Button>
        )}
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 space-y-8 overflow-y-auto">
        {/* Primary Navigation */}
        <div>
          <h3 className="px-2 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">
            Main
          </h3>
          <ul className="space-y-1">
            {navigation.map((item) => (
              <li key={item.name}>
                <Link
                  to={item.href}
                  onClick={onClose}
                  className={`
                    group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors
                    ${item.current
                      ? 'bg-green-100 text-green-700 border-r-2 border-green-600'
                      : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                    }
                  `}
                >
                  <item.icon
                    className={`
                      mr-3 h-5 w-5 flex-shrink-0
                      ${item.current ? 'text-green-600' : 'text-gray-400 group-hover:text-gray-500'}
                    `}
                  />
                  {item.name}
                </Link>
              </li>
            ))}
          </ul>
        </div>

        {/* Secondary Navigation */}
        <div>
          <h3 className="px-2 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">
            Account
          </h3>
          <ul className="space-y-1">
            {secondaryNavigation.map((item) => (
              <li key={item.name}>
                <Link
                  to={item.href}
                  onClick={onClose}
                  className={`
                    group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors
                    ${item.current
                      ? 'bg-green-100 text-green-700 border-r-2 border-green-600'
                      : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                    }
                  `}
                >
                  <item.icon
                    className={`
                      mr-3 h-5 w-5 flex-shrink-0
                      ${item.current ? 'text-green-600' : 'text-gray-400 group-hover:text-gray-500'}
                    `}
                  />
                  {item.name}
                </Link>
              </li>
            ))}
          </ul>
        </div>
      </nav>

      {/* Footer */}
      <div className="p-4 border-t">
        <Button
          variant="ghost"
          onClick={handleSignOut}
          className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
        >
          <LogOut className="mr-3 h-5 w-5" />
          Sign Out
        </Button>
      </div>
    </div>
  );
};
