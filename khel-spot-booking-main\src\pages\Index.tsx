
import { useState, useEffect } from 'react';
import { Search, MapPin, Clock, Users, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { <PERSON>, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { AuthModal } from '@/components/AuthModal';
import { BookingModal } from '@/components/BookingModal';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/integrations/supabase/client';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

const Index = () => {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [selectedVenue, setSelectedVenue] = useState(null);
  const [isBookingModalOpen, setIsBookingModalOpen] = useState(false);
  const [featuredVenues, setFeaturedVenues] = useState([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    venues: 0,
    users: 0,
    bookings: 0
  });

  // Fetch featured venues from Supabase
  const fetchFeaturedVenues = async () => {
    try {
      const { data, error } = await supabase
        .from('venues')
        .select(`
          *,
          sports:sport_id (
            name,
            icon
          )
        `)
        .eq('is_active', true)
        .order('average_rating', { ascending: false })
        .order('total_reviews', { ascending: false })
        .limit(4);

      if (error) throw error;

      // Transform the data to ensure proper format
      const transformedVenues = (data || []).map((venue: any) => ({
        ...venue,
        images: Array.isArray(venue.images) ? venue.images :
                venue.images ? [venue.images] : ["/placeholder.svg"],
        amenities: Array.isArray(venue.amenities) ? venue.amenities :
                   venue.amenities ? [venue.amenities] : []
      }));

      setFeaturedVenues(transformedVenues);
    } catch (error) {
      console.error('Error fetching featured venues:', error);
      // Fallback to empty array on error
      setFeaturedVenues([]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch dashboard stats
  const fetchStats = async () => {
    try {
      // Fetch venues count
      const { count: venuesCount } = await supabase
        .from('venues')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true);

      // Fetch users count
      const { count: usersCount } = await supabase
        .from('users')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true);

      // Fetch bookings count
      const { count: bookingsCount } = await supabase
        .from('bookings')
        .select('*', { count: 'exact', head: true });

      setStats({
        venues: venuesCount || 0,
        users: usersCount || 0,
        bookings: bookingsCount || 0
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  useEffect(() => {
    fetchFeaturedVenues();
    fetchStats();

    // Set up real-time subscription for venues
    const venuesSubscription = supabase
      .channel('featured-venues')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'venues' },
        (payload) => {
          console.log('Featured venues updated in real-time:', payload.eventType, payload.new || payload.old);
          fetchFeaturedVenues();
          fetchStats();
        }
      )
      .subscribe();

    // Set up real-time subscription for bookings
    const bookingsSubscription = supabase
      .channel('dashboard-bookings')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'bookings' },
        () => {
          fetchStats();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(venuesSubscription);
      supabase.removeChannel(bookingsSubscription);
    };
  }, []);

  const handleBookNow = (venue: any) => {
    if (!user) {
      setIsAuthModalOpen(true);
      return;
    }
    setSelectedVenue(venue);
    setIsBookingModalOpen(true);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-green-600 via-green-700 to-emerald-800 text-white py-20 px-4">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-6xl mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6 animate-fade-in">Book Your Turf in Dhaka</h1>
          <p className="text-xl md:text-2xl mb-8 text-green-100 animate-fade-in">
            Find and reserve premium sports venues across Bangladesh
          </p>
          
          {/* Search Bar */}
          <div className="max-w-2xl mx-auto bg-white rounded-full p-2 shadow-2xl animate-scale-in">
            <div className="flex items-center gap-2">
              <div className="flex-1 flex items-center gap-3 px-4">
                <Search className="text-gray-400 h-5 w-5" />
                <Input 
                  placeholder="Search by location, venue name..." 
                  value={searchQuery} 
                  onChange={e => setSearchQuery(e.target.value)} 
                  className="border-0 text-gray-800 placeholder-gray-500 focus-visible:ring-0" 
                />
              </div>
              <Button size="lg" className="bg-green-600 hover:bg-green-700 rounded-full px-8">
                <a href="/venues">Search</a>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Stats */}
      <section className="py-12 bg-white">
        <div className="max-w-6xl mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div className="animate-fade-in">
              <div className="text-3xl font-bold text-green-600 mb-2">
                {loading ? (
                  <div className="h-8 w-16 bg-gray-200 animate-pulse rounded mx-auto"></div>
                ) : (
                  `${stats.venues}+`
                )}
              </div>
              <div className="text-gray-600">Premium Venues</div>
            </div>
            <div className="animate-fade-in">
              <div className="text-3xl font-bold text-green-600 mb-2">
                {loading ? (
                  <div className="h-8 w-16 bg-gray-200 animate-pulse rounded mx-auto"></div>
                ) : (
                  `${stats.users}+`
                )}
              </div>
              <div className="text-gray-600">Happy Players</div>
            </div>
            <div className="animate-fade-in">
              <div className="text-3xl font-bold text-green-600 mb-2">
                {loading ? (
                  <div className="h-8 w-16 bg-gray-200 animate-pulse rounded mx-auto"></div>
                ) : (
                  `${stats.bookings}+`
                )}
              </div>
              <div className="text-gray-600">Games Played</div>
            </div>
            <div className="animate-fade-in">
              <div className="text-3xl font-bold text-green-600 mb-2">24/7</div>
              <div className="text-gray-600">Support</div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Venues */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
              Featured Venues
            </h2>
            <p className="text-gray-600 text-lg">
              Discover the best sports venues in your city
            </p>
          </div>

          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[1, 2, 3, 4].map((i) => (
                <Card key={i} className="border-0 shadow-lg">
                  <CardHeader className="p-0">
                    <div className="w-full h-48 bg-gray-200 animate-pulse rounded-t-lg"></div>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="h-6 bg-gray-200 animate-pulse rounded mb-2"></div>
                    <div className="h-4 bg-gray-200 animate-pulse rounded mb-2"></div>
                    <div className="h-4 bg-gray-200 animate-pulse rounded mb-3"></div>
                    <div className="flex gap-1 mb-3">
                      <div className="h-6 w-16 bg-gray-200 animate-pulse rounded-full"></div>
                      <div className="h-6 w-16 bg-gray-200 animate-pulse rounded-full"></div>
                    </div>
                  </CardContent>
                  <CardFooter className="p-4 pt-0">
                    <div className="h-8 bg-gray-200 animate-pulse rounded w-full"></div>
                  </CardFooter>
                </Card>
              ))}
            </div>
          ) : featuredVenues.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {featuredVenues.map(venue =>
                <Card key={venue.id} className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-2 cursor-pointer border-0 shadow-lg">
                  <CardHeader className="p-0">
                    <div className="relative overflow-hidden rounded-t-lg">
                      <img
                        src={venue.images?.[0] || "/placeholder.svg"}
                        alt={venue.name}
                        className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300"
                      />
                      <div className="absolute top-3 right-3 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                        {venue.is_indoor ? 'Indoor' : 'Outdoor'}
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="p-4">
                    <h3 className="font-bold text-lg mb-2 text-gray-800 group-hover:text-green-600 transition-colors">
                      {venue.name}
                    </h3>

                    <div className="flex items-center gap-1 text-gray-600 mb-2">
                      <MapPin className="h-4 w-4" />
                      <span className="text-sm">{venue.area}, {venue.city}</span>
                    </div>

                    <div className="flex items-center gap-1 mb-3">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span className="font-medium text-sm">{venue.average_rating || 0}</span>
                      <span className="text-gray-500 text-sm">({venue.total_reviews || 0} reviews)</span>
                    </div>

                    <div className="flex flex-wrap gap-1 mb-3">
                      {venue.amenities?.slice(0, 2).map((amenity, index) =>
                        <span key={index} className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                          {amenity}
                        </span>
                      )}
                    </div>
                  </CardContent>

                  <CardFooter className="p-4 pt-0 flex items-center justify-between">
                    <div>
                      <span className="text-xl font-bold text-green-600">৳{venue.price_per_hour}</span>
                      <span className="text-gray-500 text-sm">/hour</span>
                    </div>
                    <Button
                      size="sm"
                      className="bg-green-600 hover:bg-green-700"
                      onClick={() => handleBookNow(venue)}
                    >
                      Book Now
                    </Button>
                  </CardFooter>
                </Card>
              )}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-600 text-lg">No featured venues available at the moment.</p>
            </div>
          )}

          <div className="text-center mt-12">
            <Button size="lg" variant="outline" className="border-green-600 text-green-600 hover:bg-green-50">
              <a href="/venues">View All Venues</a>
            </Button>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-16 bg-green-50">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
              How It Works
            </h2>
            <p className="text-gray-600 text-lg">
              Simple steps to book your perfect venue
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center group">
              <div className="bg-green-600 text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                <Search className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-bold mb-2">Search Venues</h3>
              <p className="text-gray-600">Find the perfect venue by location, sport, or amenities</p>
            </div>

            <div className="text-center group">
              <div className="bg-green-600 text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                <Clock className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-bold mb-2">Select Time</h3>
              <p className="text-gray-600">Choose your preferred date and time slot</p>
            </div>

            <div className="text-center group">
              <div className="bg-green-600 text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                <Users className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-bold mb-2">Play & Enjoy</h3>
              <p className="text-gray-600">Show up and enjoy your game with friends</p>
            </div>
          </div>
        </div>
      </section>

      <Footer />

      {/* Modals */}
      <AuthModal 
        isOpen={isAuthModalOpen} 
        onClose={() => setIsAuthModalOpen(false)}
        onSuccess={() => setIsAuthModalOpen(false)}
      />

      <BookingModal
        isOpen={isBookingModalOpen}
        onClose={() => setIsBookingModalOpen(false)}
        venue={selectedVenue}
        onBookingSuccess={() => {
          setIsBookingModalOpen(false);
        }}
      />
    </div>
  );
};

export default Index;
