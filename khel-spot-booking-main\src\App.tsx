import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/hooks/useAuth";
// import { AdminProvider } from "@/hooks/useAdmin";
import Index from "./pages/Index";
import Venues from "./pages/Venues";
import Login from "./pages/Login";
import Signup from "./pages/Signup";
import Admin from "./pages/Admin";
import AdminLogin from "./pages/AdminLogin";
import TestAuth from "./pages/TestAuth";
import NotFound from "./pages/NotFound";
import UserDashboard from "./pages/UserDashboard";
import UserBookings from "./pages/UserBookings";
import UserFavorites from "./pages/UserFavorites";
import UserProfile from "./pages/UserProfile";
import UserPayments from "./pages/UserPayments";
import UserReviews from "./pages/UserReviews";
import UserNotifications from "./pages/UserNotifications";
import UserSettings from "./pages/UserSettings";
import UserSupport from "./pages/UserSupport";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <AuthProvider>
        <BrowserRouter>
          <Routes>
            <Route path="/test-auth" element={<TestAuth />} />
            <Route path="/" element={<Index />} />
            <Route path="/venues" element={<Venues />} />
            <Route path="/login" element={<Login />} />
            <Route path="/signup" element={<Signup />} />
            <Route path="/dashboard" element={<UserDashboard />} />
            <Route path="/dashboard/bookings" element={<UserBookings />} />
            <Route path="/dashboard/favorites" element={<UserFavorites />} />
            <Route path="/dashboard/profile" element={<UserProfile />} />
            <Route path="/dashboard/payments" element={<UserPayments />} />
            <Route path="/dashboard/reviews" element={<UserReviews />} />
            <Route path="/dashboard/notifications" element={<UserNotifications />} />
            <Route path="/dashboard/settings" element={<UserSettings />} />
            <Route path="/dashboard/support" element={<UserSupport />} />
            <Route path="/admin/login" element={<AdminLogin />} />
            <Route path="/admin" element={<Admin />} />
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </AuthProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
