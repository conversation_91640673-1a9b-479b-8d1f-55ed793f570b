import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { Navigate } from 'react-router-dom';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Heart, 
  MapPin, 
  Star, 
  Calendar, 
  Search,
  Eye,
  Trash2,
  Clock,
  Users
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { Link } from 'react-router-dom';

const UserFavorites = () => {
  const { user, loading } = useAuth();
  const [favorites, setFavorites] = useState([]);
  const [filteredFavorites, setFilteredFavorites] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  // Redirect to login if not authenticated
  if (!loading && !user) {
    return <Navigate to="/login" replace />;
  }

  // Fetch user's favorite venues
  const fetchFavorites = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      const { data, error } = await supabase
        .from('favorites')
        .select(`
          *,
          venues:venue_id (
            id,
            name,
            slug,
            description,
            city,
            area,
            images,
            average_rating,
            total_reviews,
            price_per_hour,
            currency,
            amenities,
            is_indoor,
            opening_time,
            closing_time,
            surface_type
          )
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setFavorites(data || []);
      setFilteredFavorites(data || []);
    } catch (error) {
      console.error('Error fetching favorites:', error);
      toast.error('Failed to load favorite venues');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchFavorites();
  }, [user]);

  // Filter favorites based on search query
  useEffect(() => {
    if (!searchQuery) {
      setFilteredFavorites(favorites);
      return;
    }

    const filtered = favorites.filter(favorite =>
      favorite.venues?.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      favorite.venues?.area.toLowerCase().includes(searchQuery.toLowerCase()) ||
      favorite.venues?.city.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredFavorites(filtered);
  }, [favorites, searchQuery]);

  // Remove from favorites
  const removeFavorite = async (favoriteId: number, venueName: string) => {
    try {
      const { error } = await supabase
        .from('favorites')
        .delete()
        .eq('id', favoriteId);

      if (error) throw error;

      setFavorites(prev => prev.filter(fav => fav.id !== favoriteId));
      toast.success(`${venueName} removed from favorites`);
    } catch (error) {
      console.error('Error removing favorite:', error);
      toast.error('Failed to remove from favorites');
    }
  };

  if (loading || isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading your favorite venues...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Favorite Venues</h1>
            <p className="text-gray-600">Your saved venues for quick booking</p>
          </div>
          
          <Button className="bg-green-600 hover:bg-green-700" asChild>
            <Link to="/venues">
              <Search className="h-4 w-4 mr-2" />
              Discover More Venues
            </Link>
          </Button>
        </div>

        {/* Search */}
        <Card>
          <CardContent className="p-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search your favorite venues..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </CardContent>
        </Card>

        {/* Favorites Grid */}
        {filteredFavorites.length === 0 ? (
          <Card>
            <CardContent className="p-12 text-center">
              <Heart className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-medium text-gray-900 mb-2">
                {searchQuery ? 'No matching favorites found' : 'No favorite venues yet'}
              </h3>
              <p className="text-gray-500 mb-6">
                {searchQuery 
                  ? 'Try adjusting your search terms'
                  : 'Start exploring venues and save your favorites for quick access'
                }
              </p>
              <Button asChild>
                <Link to="/venues">Browse Venues</Link>
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredFavorites.map((favorite) => (
              <FavoriteVenueCard
                key={favorite.id}
                favorite={favorite}
                onRemove={() => removeFavorite(favorite.id, favorite.venues.name)}
              />
            ))}
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

// Individual Favorite Venue Card
const FavoriteVenueCard = ({ favorite, onRemove }: { favorite: any; onRemove: () => void }) => {
  const venue = favorite.venues;

  return (
    <Card className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
      <div className="relative">
        <img
          src={venue.images?.[0] || '/placeholder.svg'}
          alt={venue.name}
          className="w-full h-48 object-cover rounded-t-lg"
        />
        
        {/* Favorite Heart Button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={onRemove}
          className="absolute top-3 right-3 bg-white/90 hover:bg-white text-red-500 hover:text-red-600 rounded-full p-2"
        >
          <Heart className="h-4 w-4 fill-current" />
        </Button>

        {/* Indoor/Outdoor Badge */}
        <Badge 
          className="absolute top-3 left-3 bg-green-500 text-white"
        >
          {venue.is_indoor ? 'Indoor' : 'Outdoor'}
        </Badge>
      </div>

      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Venue Name and Location */}
          <div>
            <h3 className="font-bold text-lg text-gray-900 group-hover:text-green-600 transition-colors">
              {venue.name}
            </h3>
            <div className="flex items-center text-sm text-gray-500">
              <MapPin className="h-3 w-3 mr-1" />
              {venue.area}, {venue.city}
            </div>
          </div>

          {/* Rating */}
          <div className="flex items-center space-x-2">
            <div className="flex items-center">
              <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
              <span className="text-sm font-medium ml-1">
                {venue.average_rating || 0}
              </span>
            </div>
            <span className="text-sm text-gray-500">
              ({venue.total_reviews || 0} reviews)
            </span>
          </div>

          {/* Amenities */}
          {venue.amenities && venue.amenities.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {venue.amenities.slice(0, 3).map((amenity: string, index: number) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {amenity}
                </Badge>
              ))}
              {venue.amenities.length > 3 && (
                <Badge variant="secondary" className="text-xs">
                  +{venue.amenities.length - 3} more
                </Badge>
              )}
            </div>
          )}

          {/* Operating Hours */}
          {venue.opening_time && venue.closing_time && (
            <div className="flex items-center text-sm text-gray-500">
              <Clock className="h-3 w-3 mr-1" />
              {venue.opening_time} - {venue.closing_time}
            </div>
          )}

          {/* Surface Type */}
          {venue.surface_type && (
            <div className="flex items-center text-sm text-gray-500">
              <Users className="h-3 w-3 mr-1" />
              {venue.surface_type} surface
            </div>
          )}

          {/* Price and Actions */}
          <div className="flex items-center justify-between pt-3 border-t">
            <div>
              <span className="text-xl font-bold text-green-600">
                ৳{parseFloat(venue.price_per_hour).toLocaleString()}
              </span>
              <span className="text-sm text-gray-500">/hour</span>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" asChild>
                <Link to={`/venues/${venue.id}`}>
                  <Eye className="h-3 w-3 mr-1" />
                  View
                </Link>
              </Button>
              
              <Button size="sm" className="bg-green-600 hover:bg-green-700" asChild>
                <Link to={`/venues/${venue.id}?book=true`}>
                  <Calendar className="h-3 w-3 mr-1" />
                  Book
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default UserFavorites;
