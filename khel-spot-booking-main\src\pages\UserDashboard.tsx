import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { Navigate } from 'react-router-dom';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { OverviewCards } from '@/components/dashboard/OverviewCards';
import { BookingsList } from '@/components/dashboard/BookingsList';
import { QuickBookingWidget } from '@/components/dashboard/QuickBookingWidget';
import { FavoriteVenues } from '@/components/dashboard/FavoriteVenues';
import { RecentActivity } from '@/components/dashboard/RecentActivity';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Calendar, MapPin, Star, Clock, CreditCard, Settings } from 'lucide-react';

const UserDashboard = () => {
  const { user, loading } = useAuth();
  const [dashboardData, setDashboardData] = useState({
    upcomingBookings: [],
    recentBookings: [],
    favoriteVenues: [],
    stats: {
      totalBookings: 0,
      upcomingBookings: 0,
      completedBookings: 0,
      totalSpent: 0
    }
  });
  const [isLoading, setIsLoading] = useState(true);

  // Redirect to login if not authenticated
  if (!loading && !user) {
    return <Navigate to="/login" replace />;
  }

  // Fetch dashboard data
  const fetchDashboardData = async () => {
    if (!user) return;

    try {
      setIsLoading(true);

      // Fetch user's bookings
      const { data: bookings, error: bookingsError } = await supabase
        .from('bookings')
        .select(`
          *,
          venues:venue_id (
            id,
            name,
            city,
            area,
            images,
            price_per_hour
          )
        `)
        .eq('user_id', user.id)
        .order('booking_date', { ascending: false });

      if (bookingsError) throw bookingsError;

      // Fetch user's favorite venues
      const { data: favorites, error: favoritesError } = await supabase
        .from('favorites')
        .select(`
          *,
          venues:venue_id (
            id,
            name,
            city,
            area,
            images,
            average_rating,
            price_per_hour
          )
        `)
        .eq('user_id', user.id);

      if (favoritesError) throw favoritesError;

      // Process bookings data
      const now = new Date();
      const today = now.toISOString().split('T')[0];
      
      const upcomingBookings = (bookings || []).filter(booking => 
        booking.booking_date >= today && 
        ['pending', 'confirmed'].includes(booking.status)
      );

      const completedBookings = (bookings || []).filter(booking => 
        booking.status === 'completed'
      );

      const totalSpent = (bookings || [])
        .filter(booking => booking.payment_status === 'paid')
        .reduce((sum, booking) => sum + parseFloat(booking.total_amount || 0), 0);

      setDashboardData({
        upcomingBookings: upcomingBookings.slice(0, 5),
        recentBookings: (bookings || []).slice(0, 5),
        favoriteVenues: (favorites || []).slice(0, 4),
        stats: {
          totalBookings: (bookings || []).length,
          upcomingBookings: upcomingBookings.length,
          completedBookings: completedBookings.length,
          totalSpent
        }
      });

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();

    // Set up real-time subscription for bookings
    const bookingsSubscription = supabase
      .channel('user-bookings')
      .on('postgres_changes',
        { 
          event: '*', 
          schema: 'public', 
          table: 'bookings',
          filter: `user_id=eq.${user?.id}`
        },
        () => {
          fetchDashboardData();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(bookingsSubscription);
    };
  }, [user]);

  if (loading || isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="bg-gradient-to-r from-green-600 to-green-700 rounded-lg p-6 text-white">
          <h1 className="text-2xl font-bold mb-2">
            Welcome back, {user?.user_metadata?.full_name || user?.email}!
          </h1>
          <p className="text-green-100">
            Ready to book your next game? Let's get you on the field!
          </p>
        </div>

        {/* Overview Cards */}
        <OverviewCards stats={dashboardData.stats} />

        {/* Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Quick Booking */}
          <div className="lg:col-span-2">
            <QuickBookingWidget />
          </div>

          {/* Quick Actions Panel */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Quick Actions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button variant="outline" className="w-full justify-start" asChild>
                <a href="/dashboard/bookings">
                  <Calendar className="h-4 w-4 mr-2" />
                  View All Bookings
                </a>
              </Button>
              <Button variant="outline" className="w-full justify-start" asChild>
                <a href="/venues">
                  <MapPin className="h-4 w-4 mr-2" />
                  Browse Venues
                </a>
              </Button>
              <Button variant="outline" className="w-full justify-start" asChild>
                <a href="/dashboard/favorites">
                  <Star className="h-4 w-4 mr-2" />
                  My Favorites
                </a>
              </Button>
              <Button variant="outline" className="w-full justify-start" asChild>
                <a href="/dashboard/payments">
                  <CreditCard className="h-4 w-4 mr-2" />
                  Payment History
                </a>
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Upcoming Bookings */}
          <BookingsList 
            title="Upcoming Bookings"
            bookings={dashboardData.upcomingBookings}
            showViewAll={dashboardData.stats.upcomingBookings > 5}
          />

          {/* Favorite Venues */}
          <FavoriteVenues 
            venues={dashboardData.favoriteVenues}
            showViewAll={true}
          />
        </div>

        {/* Recent Activity */}
        <RecentActivity bookings={dashboardData.recentBookings} />
      </div>
    </DashboardLayout>
  );
};

export default UserDashboard;
