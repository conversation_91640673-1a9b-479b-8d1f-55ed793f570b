import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { Navigate } from 'react-router-dom';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Bell, 
  Check, 
  Trash2, 
  Settings,
  Calendar,
  CreditCard,
  Star,
  AlertCircle,
  CheckCircle,
  Info,
  X
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { format, formatDistanceToNow } from 'date-fns';

const UserNotifications = () => {
  const { user, loading } = useAuth();
  const [notifications, setNotifications] = useState([]);
  const [preferences, setPreferences] = useState({
    email_bookings: true,
    email_payments: true,
    email_promotions: false,
    push_bookings: true,
    push_payments: true,
    push_promotions: false,
    sms_bookings: false,
    sms_payments: true,
    sms_promotions: false
  });
  const [isLoading, setIsLoading] = useState(true);

  // Redirect to login if not authenticated
  if (!loading && !user) {
    return <Navigate to="/login" replace />;
  }

  // Fetch notifications
  const fetchNotifications = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      
      // For now, we'll create mock notifications based on user's bookings
      const { data: bookings } = await supabase
        .from('bookings')
        .select(`
          *,
          venues:venue_id (name)
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(10);

      // Generate notifications from bookings
      const mockNotifications = (bookings || []).map((booking, index) => ({
        id: `notif_${booking.id}_${index}`,
        type: getNotificationType(booking.status),
        title: getNotificationTitle(booking.status, booking.venues?.name),
        message: getNotificationMessage(booking),
        read: Math.random() > 0.3, // Random read status
        created_at: booking.updated_at || booking.created_at,
        booking_id: booking.id,
        venue_name: booking.venues?.name
      }));

      setNotifications(mockNotifications);
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getNotificationType = (status: string) => {
    switch (status) {
      case 'confirmed': return 'success';
      case 'completed': return 'info';
      case 'cancelled': return 'error';
      case 'pending': return 'warning';
      default: return 'info';
    }
  };

  const getNotificationTitle = (status: string, venueName: string) => {
    switch (status) {
      case 'confirmed': return `Booking Confirmed - ${venueName}`;
      case 'completed': return `Game Completed - ${venueName}`;
      case 'cancelled': return `Booking Cancelled - ${venueName}`;
      case 'pending': return `Booking Pending - ${venueName}`;
      default: return `Booking Update - ${venueName}`;
    }
  };

  const getNotificationMessage = (booking: any) => {
    const date = format(new Date(booking.booking_date), 'MMM dd, yyyy');
    const time = booking.start_time;
    
    switch (booking.status) {
      case 'confirmed':
        return `Your booking for ${date} at ${time} has been confirmed. Get ready to play!`;
      case 'completed':
        return `Hope you had a great game on ${date}! Don't forget to leave a review.`;
      case 'cancelled':
        return `Your booking for ${date} at ${time} has been cancelled. Refund will be processed if applicable.`;
      case 'pending':
        return `Your booking for ${date} at ${time} is pending confirmation. We'll notify you once confirmed.`;
      default:
        return `Your booking status has been updated.`;
    }
  };

  useEffect(() => {
    fetchNotifications();
  }, [user]);

  // Mark notification as read
  const markAsRead = async (notificationId: string) => {
    setNotifications(prev => 
      prev.map(notif => 
        notif.id === notificationId ? { ...notif, read: true } : notif
      )
    );
    toast.success('Notification marked as read');
  };

  // Mark all as read
  const markAllAsRead = async () => {
    setNotifications(prev => 
      prev.map(notif => ({ ...notif, read: true }))
    );
    toast.success('All notifications marked as read');
  };

  // Delete notification
  const deleteNotification = async (notificationId: string) => {
    setNotifications(prev => 
      prev.filter(notif => notif.id !== notificationId)
    );
    toast.success('Notification deleted');
  };

  // Update preferences
  const updatePreference = async (key: string, value: boolean) => {
    setPreferences(prev => ({ ...prev, [key]: value }));
    
    try {
      // In a real app, you'd save this to the database
      // For now, we'll just show a success message
      toast.success('Notification preferences updated');
    } catch (error) {
      console.error('Error updating preferences:', error);
      toast.error('Failed to update preferences');
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success': return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error': return <X className="h-5 w-5 text-red-500" />;
      case 'warning': return <AlertCircle className="h-5 w-5 text-yellow-500" />;
      default: return <Info className="h-5 w-5 text-blue-500" />;
    }
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  if (loading || isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading notifications...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Notifications</h1>
            <p className="text-gray-600">
              Stay updated with your bookings and account activity
              {unreadCount > 0 && (
                <Badge className="ml-2 bg-red-100 text-red-800">
                  {unreadCount} unread
                </Badge>
              )}
            </p>
          </div>
          
          {unreadCount > 0 && (
            <Button variant="outline" onClick={markAllAsRead}>
              <Check className="h-4 w-4 mr-2" />
              Mark All Read
            </Button>
          )}
        </div>

        {/* Notifications Tabs */}
        <Tabs defaultValue="all" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="all">All ({notifications.length})</TabsTrigger>
            <TabsTrigger value="unread">Unread ({unreadCount})</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="all">
            <NotificationsList 
              notifications={notifications}
              onMarkAsRead={markAsRead}
              onDelete={deleteNotification}
            />
          </TabsContent>

          <TabsContent value="unread">
            <NotificationsList 
              notifications={notifications.filter(n => !n.read)}
              onMarkAsRead={markAsRead}
              onDelete={deleteNotification}
            />
          </TabsContent>

          <TabsContent value="settings">
            <NotificationSettings 
              preferences={preferences}
              onUpdatePreference={updatePreference}
            />
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

// Notifications List Component
const NotificationsList = ({ notifications, onMarkAsRead, onDelete }: any) => {
  if (notifications.length === 0) {
    return (
      <Card>
        <CardContent className="p-12 text-center">
          <Bell className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-medium text-gray-900 mb-2">No notifications</h3>
          <p className="text-gray-500">You're all caught up! New notifications will appear here.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-3">
      {notifications.map((notification: any) => (
        <NotificationCard
          key={notification.id}
          notification={notification}
          onMarkAsRead={onMarkAsRead}
          onDelete={onDelete}
        />
      ))}
    </div>
  );
};

// Individual Notification Card
const NotificationCard = ({ notification, onMarkAsRead, onDelete }: any) => {
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success': return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error': return <X className="h-5 w-5 text-red-500" />;
      case 'warning': return <AlertCircle className="h-5 w-5 text-yellow-500" />;
      default: return <Info className="h-5 w-5 text-blue-500" />;
    }
  };

  return (
    <Card className={`hover:shadow-md transition-shadow ${!notification.read ? 'border-l-4 border-l-green-500 bg-green-50/30' : ''}`}>
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3 flex-1">
            <div className="flex-shrink-0 mt-1">
              {getNotificationIcon(notification.type)}
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 mb-1">
                <h4 className={`font-medium ${!notification.read ? 'text-gray-900' : 'text-gray-700'}`}>
                  {notification.title}
                </h4>
                {!notification.read && (
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                )}
              </div>
              
              <p className="text-sm text-gray-600 mb-2">
                {notification.message}
              </p>
              
              <p className="text-xs text-gray-400">
                {formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {!notification.read && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onMarkAsRead(notification.id)}
              >
                <Check className="h-4 w-4" />
              </Button>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDelete(notification.id)}
              className="text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Notification Settings Component
const NotificationSettings = ({ preferences, onUpdatePreference }: any) => {
  const settingsGroups = [
    {
      title: 'Booking Notifications',
      description: 'Get notified about your booking status and updates',
      settings: [
        { key: 'email_bookings', label: 'Email notifications', type: 'email' },
        { key: 'push_bookings', label: 'Push notifications', type: 'push' },
        { key: 'sms_bookings', label: 'SMS notifications', type: 'sms' }
      ]
    },
    {
      title: 'Payment Notifications',
      description: 'Stay informed about payment confirmations and receipts',
      settings: [
        { key: 'email_payments', label: 'Email notifications', type: 'email' },
        { key: 'push_payments', label: 'Push notifications', type: 'push' },
        { key: 'sms_payments', label: 'SMS notifications', type: 'sms' }
      ]
    },
    {
      title: 'Promotional Notifications',
      description: 'Receive updates about offers, discounts, and new venues',
      settings: [
        { key: 'email_promotions', label: 'Email notifications', type: 'email' },
        { key: 'push_promotions', label: 'Push notifications', type: 'push' },
        { key: 'sms_promotions', label: 'SMS notifications', type: 'sms' }
      ]
    }
  ];

  return (
    <div className="space-y-6">
      {settingsGroups.map((group) => (
        <Card key={group.title}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              {group.title}
            </CardTitle>
            <p className="text-sm text-gray-600">{group.description}</p>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {group.settings.map((setting) => (
                <div key={setting.key} className="flex items-center justify-between">
                  <Label htmlFor={setting.key} className="flex items-center space-x-2">
                    {setting.type === 'email' && <CreditCard className="h-4 w-4" />}
                    {setting.type === 'push' && <Bell className="h-4 w-4" />}
                    {setting.type === 'sms' && <AlertCircle className="h-4 w-4" />}
                    <span>{setting.label}</span>
                  </Label>
                  <Switch
                    id={setting.key}
                    checked={preferences[setting.key]}
                    onCheckedChange={(checked) => onUpdatePreference(setting.key, checked)}
                  />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default UserNotifications;
