import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { Navigate } from 'react-router-dom';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  Star, 
  MessageSquare, 
  Search,
  Edit,
  Trash2,
  Plus,
  MapPin,
  Calendar
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { format } from 'date-fns';

const UserReviews = () => {
  const { user, loading } = useAuth();
  const [reviews, setReviews] = useState([]);
  const [filteredReviews, setFilteredReviews] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  // Redirect to login if not authenticated
  if (!loading && !user) {
    return <Navigate to="/login" replace />;
  }

  // Fetch user's reviews
  const fetchReviews = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      const { data, error } = await supabase
        .from('reviews')
        .select(`
          *,
          venues:venue_id (
            id,
            name,
            city,
            area,
            images
          ),
          bookings:booking_id (
            id,
            booking_number,
            booking_date
          )
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setReviews(data || []);
      setFilteredReviews(data || []);
    } catch (error) {
      console.error('Error fetching reviews:', error);
      toast.error('Failed to load reviews');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchReviews();
  }, [user]);

  // Filter reviews based on search query
  useEffect(() => {
    if (!searchQuery) {
      setFilteredReviews(reviews);
      return;
    }

    const filtered = reviews.filter(review =>
      review.venues?.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      review.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      review.comment.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredReviews(filtered);
  }, [reviews, searchQuery]);

  // Create new review
  const createReview = async (reviewData: any) => {
    try {
      const { data, error } = await supabase
        .from('reviews')
        .insert({
          user_id: user.id,
          venue_id: reviewData.venue_id,
          booking_id: reviewData.booking_id,
          rating: reviewData.rating,
          title: reviewData.title,
          comment: reviewData.comment,
          created_at: new Date().toISOString()
        })
        .select(`
          *,
          venues:venue_id (
            id,
            name,
            city,
            area,
            images
          ),
          bookings:booking_id (
            id,
            booking_number,
            booking_date
          )
        `)
        .single();

      if (error) throw error;

      setReviews(prev => [data, ...prev]);
      toast.success('Review created successfully');
      return data;
    } catch (error) {
      console.error('Error creating review:', error);
      toast.error('Failed to create review');
      throw error;
    }
  };

  if (loading || isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading your reviews...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Reviews</h1>
            <p className="text-gray-600">Manage your venue reviews and ratings</p>
          </div>
          
          <Button className="bg-green-600 hover:bg-green-700">
            <Plus className="h-4 w-4 mr-2" />
            Write Review
          </Button>
        </div>

        {/* Search */}
        <Card>
          <CardContent className="p-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search your reviews..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </CardContent>
        </Card>

        {/* Reviews List */}
        {filteredReviews.length === 0 ? (
          <Card>
            <CardContent className="p-12 text-center">
              <MessageSquare className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-medium text-gray-900 mb-2">
                {searchQuery ? 'No matching reviews found' : 'No reviews yet'}
              </h3>
              <p className="text-gray-500 mb-6">
                {searchQuery 
                  ? 'Try adjusting your search terms'
                  : 'Share your experience by writing your first review'
                }
              </p>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Write Your First Review
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            {filteredReviews.map((review) => (
              <ReviewCard
                key={review.id}
                review={review}
                onUpdate={fetchReviews}
              />
            ))}
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

// Individual Review Card
const ReviewCard = ({ review, onUpdate }: { review: any; onUpdate: () => void }) => {
  const [isEditing, setIsEditing] = useState(false);

  const deleteReview = async () => {
    try {
      const { error } = await supabase
        .from('reviews')
        .delete()
        .eq('id', review.id);

      if (error) throw error;

      toast.success('Review deleted successfully');
      onUpdate();
    } catch (error) {
      console.error('Error deleting review:', error);
      toast.error('Failed to delete review');
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-6">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            {/* Venue Info */}
            <div className="flex items-center space-x-4 mb-4">
              <img
                src={review.venues?.images?.[0] || '/placeholder.svg'}
                alt={review.venues?.name}
                className="w-12 h-12 rounded-lg object-cover"
              />
              <div>
                <h3 className="font-semibold text-gray-900">
                  {review.venues?.name}
                </h3>
                <div className="flex items-center text-sm text-gray-500">
                  <MapPin className="h-3 w-3 mr-1" />
                  {review.venues?.area}, {review.venues?.city}
                </div>
              </div>
            </div>

            {/* Review Content */}
            <div className="space-y-3">
              {/* Rating and Title */}
              <div>
                <div className="flex items-center space-x-2 mb-2">
                  <div className="flex items-center">
                    {renderStars(review.rating)}
                  </div>
                  <span className="text-sm text-gray-500">
                    {format(new Date(review.created_at), 'MMM dd, yyyy')}
                  </span>
                </div>
                <h4 className="font-semibold text-lg text-gray-900">
                  {review.title}
                </h4>
              </div>

              {/* Review Text */}
              <p className="text-gray-700 leading-relaxed">
                {review.comment}
              </p>

              {/* Booking Info */}
              {review.bookings && (
                <div className="flex items-center text-sm text-gray-500 pt-2 border-t">
                  <Calendar className="h-3 w-3 mr-1" />
                  Booking: {review.bookings.booking_number} on{' '}
                  {format(new Date(review.bookings.booking_date), 'MMM dd, yyyy')}
                </div>
              )}

              {/* Status Badges */}
              <div className="flex items-center space-x-2">
                {review.is_approved ? (
                  <Badge className="bg-green-100 text-green-800">Approved</Badge>
                ) : (
                  <Badge className="bg-yellow-100 text-yellow-800">Pending Review</Badge>
                )}
                {review.is_featured && (
                  <Badge className="bg-blue-100 text-blue-800">Featured</Badge>
                )}
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-2">
            <Dialog open={isEditing} onOpenChange={setIsEditing}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <Edit className="h-3 w-3 mr-1" />
                  Edit
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Edit Review</DialogTitle>
                </DialogHeader>
                <EditReviewForm
                  review={review}
                  onSuccess={() => {
                    setIsEditing(false);
                    onUpdate();
                  }}
                />
              </DialogContent>
            </Dialog>

            <Button
              variant="outline"
              size="sm"
              onClick={deleteReview}
              className="text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <Trash2 className="h-3 w-3 mr-1" />
              Delete
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Edit Review Form
const EditReviewForm = ({ review, onSuccess }: { review: any; onSuccess: () => void }) => {
  const [rating, setRating] = useState(review.rating);
  const [title, setTitle] = useState(review.title);
  const [comment, setComment] = useState(review.comment);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const { error } = await supabase
        .from('reviews')
        .update({
          rating,
          title,
          comment,
          updated_at: new Date().toISOString()
        })
        .eq('id', review.id);

      if (error) throw error;

      toast.success('Review updated successfully');
      onSuccess();
    } catch (error) {
      console.error('Error updating review:', error);
      toast.error('Failed to update review');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* Rating */}
      <div className="space-y-2">
        <label className="text-sm font-medium">Rating</label>
        <div className="flex items-center space-x-1">
          {Array.from({ length: 5 }, (_, i) => (
            <button
              key={i}
              type="button"
              onClick={() => setRating(i + 1)}
              className="focus:outline-none"
            >
              <Star
                className={`h-6 w-6 ${
                  i < rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
                }`}
              />
            </button>
          ))}
        </div>
      </div>

      {/* Title */}
      <div className="space-y-2">
        <label className="text-sm font-medium">Title</label>
        <Input
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          placeholder="Review title"
          required
        />
      </div>

      {/* Comment */}
      <div className="space-y-2">
        <label className="text-sm font-medium">Review</label>
        <Textarea
          value={comment}
          onChange={(e) => setComment(e.target.value)}
          placeholder="Share your experience..."
          rows={4}
          required
        />
      </div>

      {/* Submit */}
      <Button type="submit" disabled={isLoading} className="w-full">
        {isLoading ? 'Updating...' : 'Update Review'}
      </Button>
    </form>
  );
};

export default UserReviews;
