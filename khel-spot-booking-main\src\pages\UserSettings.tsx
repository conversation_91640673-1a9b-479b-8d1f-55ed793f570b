import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { Navigate } from 'react-router-dom';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Settings, 
  Shield, 
  Bell, 
  Globe,
  Moon,
  Sun,
  Smartphone,
  Mail,
  Lock,
  Eye,
  EyeOff,
  Trash2,
  Download,
  AlertTriangle
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

const UserSettings = () => {
  const { user, loading, signOut } = useAuth();
  const [settings, setSettings] = useState({
    // Privacy Settings
    profile_visibility: 'public',
    show_booking_history: false,
    show_reviews: true,
    
    // Notification Settings
    email_notifications: true,
    push_notifications: true,
    sms_notifications: false,
    marketing_emails: false,
    
    // App Preferences
    theme: 'light',
    language: 'en',
    currency: 'BDT',
    timezone: 'Asia/Dhaka',
    
    // Security Settings
    two_factor_enabled: false,
    login_alerts: true,
    session_timeout: '30'
  });
  const [isLoading, setIsLoading] = useState(false);

  // Redirect to login if not authenticated
  if (!loading && !user) {
    return <Navigate to="/login" replace />;
  }

  // Load user settings
  useEffect(() => {
    const loadSettings = async () => {
      if (!user) return;

      try {
        // In a real app, you'd fetch settings from the database
        // For now, we'll use default settings
        const savedSettings = localStorage.getItem(`user_settings_${user.id}`);
        if (savedSettings) {
          setSettings(prev => ({ ...prev, ...JSON.parse(savedSettings) }));
        }
      } catch (error) {
        console.error('Error loading settings:', error);
      }
    };

    loadSettings();
  }, [user]);

  // Save settings
  const saveSettings = async (newSettings: any) => {
    setIsLoading(true);
    try {
      // In a real app, you'd save to the database
      localStorage.setItem(`user_settings_${user?.id}`, JSON.stringify(newSettings));
      setSettings(newSettings);
      toast.success('Settings saved successfully');
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error('Failed to save settings');
    } finally {
      setIsLoading(false);
    }
  };

  const updateSetting = (key: string, value: any) => {
    const newSettings = { ...settings, [key]: value };
    saveSettings(newSettings);
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading settings...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Account Settings</h1>
          <p className="text-gray-600">Manage your account preferences and security settings</p>
        </div>

        {/* Settings Tabs */}
        <Tabs defaultValue="general" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="privacy">Privacy</TabsTrigger>
            <TabsTrigger value="notifications">Notifications</TabsTrigger>
            <TabsTrigger value="security">Security</TabsTrigger>
            <TabsTrigger value="data">Data</TabsTrigger>
          </TabsList>

          <TabsContent value="general">
            <GeneralSettings settings={settings} onUpdate={updateSetting} />
          </TabsContent>

          <TabsContent value="privacy">
            <PrivacySettings settings={settings} onUpdate={updateSetting} />
          </TabsContent>

          <TabsContent value="notifications">
            <NotificationSettings settings={settings} onUpdate={updateSetting} />
          </TabsContent>

          <TabsContent value="security">
            <SecuritySettings settings={settings} onUpdate={updateSetting} />
          </TabsContent>

          <TabsContent value="data">
            <DataSettings user={user} onSignOut={signOut} />
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

// General Settings Component
const GeneralSettings = ({ settings, onUpdate }: any) => {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            App Preferences
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Theme */}
          <div className="space-y-2">
            <Label>Theme</Label>
            <Select value={settings.theme} onValueChange={(value) => onUpdate('theme', value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="light">
                  <div className="flex items-center">
                    <Sun className="h-4 w-4 mr-2" />
                    Light
                  </div>
                </SelectItem>
                <SelectItem value="dark">
                  <div className="flex items-center">
                    <Moon className="h-4 w-4 mr-2" />
                    Dark
                  </div>
                </SelectItem>
                <SelectItem value="system">
                  <div className="flex items-center">
                    <Smartphone className="h-4 w-4 mr-2" />
                    System
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Language */}
          <div className="space-y-2">
            <Label>Language</Label>
            <Select value={settings.language} onValueChange={(value) => onUpdate('language', value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="en">English</SelectItem>
                <SelectItem value="bn">বাংলা (Bengali)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Currency */}
          <div className="space-y-2">
            <Label>Currency</Label>
            <Select value={settings.currency} onValueChange={(value) => onUpdate('currency', value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="BDT">৳ Bangladeshi Taka (BDT)</SelectItem>
                <SelectItem value="USD">$ US Dollar (USD)</SelectItem>
                <SelectItem value="EUR">€ Euro (EUR)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Timezone */}
          <div className="space-y-2">
            <Label>Timezone</Label>
            <Select value={settings.timezone} onValueChange={(value) => onUpdate('timezone', value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Asia/Dhaka">Asia/Dhaka (GMT+6)</SelectItem>
                <SelectItem value="UTC">UTC (GMT+0)</SelectItem>
                <SelectItem value="America/New_York">America/New_York (GMT-5)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Privacy Settings Component
const PrivacySettings = ({ settings, onUpdate }: any) => {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Profile Visibility
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Profile Visibility</Label>
            <Select value={settings.profile_visibility} onValueChange={(value) => onUpdate('profile_visibility', value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="public">Public - Anyone can see your profile</SelectItem>
                <SelectItem value="friends">Friends Only - Only your friends can see</SelectItem>
                <SelectItem value="private">Private - Only you can see</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label>Show Booking History</Label>
              <p className="text-sm text-gray-500">Allow others to see your past bookings</p>
            </div>
            <Switch
              checked={settings.show_booking_history}
              onCheckedChange={(checked) => onUpdate('show_booking_history', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label>Show Reviews</Label>
              <p className="text-sm text-gray-500">Display your reviews publicly</p>
            </div>
            <Switch
              checked={settings.show_reviews}
              onCheckedChange={(checked) => onUpdate('show_reviews', checked)}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Notification Settings Component
const NotificationSettings = ({ settings, onUpdate }: any) => {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Notification Preferences
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label>Email Notifications</Label>
              <p className="text-sm text-gray-500">Receive notifications via email</p>
            </div>
            <Switch
              checked={settings.email_notifications}
              onCheckedChange={(checked) => onUpdate('email_notifications', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label>Push Notifications</Label>
              <p className="text-sm text-gray-500">Receive push notifications in browser</p>
            </div>
            <Switch
              checked={settings.push_notifications}
              onCheckedChange={(checked) => onUpdate('push_notifications', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label>SMS Notifications</Label>
              <p className="text-sm text-gray-500">Receive important updates via SMS</p>
            </div>
            <Switch
              checked={settings.sms_notifications}
              onCheckedChange={(checked) => onUpdate('sms_notifications', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label>Marketing Emails</Label>
              <p className="text-sm text-gray-500">Receive promotional offers and updates</p>
            </div>
            <Switch
              checked={settings.marketing_emails}
              onCheckedChange={(checked) => onUpdate('marketing_emails', checked)}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Security Settings Component
const SecuritySettings = ({ settings, onUpdate }: any) => {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Security Options
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label>Two-Factor Authentication</Label>
              <p className="text-sm text-gray-500">Add an extra layer of security</p>
            </div>
            <Switch
              checked={settings.two_factor_enabled}
              onCheckedChange={(checked) => onUpdate('two_factor_enabled', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label>Login Alerts</Label>
              <p className="text-sm text-gray-500">Get notified of new login attempts</p>
            </div>
            <Switch
              checked={settings.login_alerts}
              onCheckedChange={(checked) => onUpdate('login_alerts', checked)}
            />
          </div>

          <div className="space-y-2">
            <Label>Session Timeout</Label>
            <Select value={settings.session_timeout} onValueChange={(value) => onUpdate('session_timeout', value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="15">15 minutes</SelectItem>
                <SelectItem value="30">30 minutes</SelectItem>
                <SelectItem value="60">1 hour</SelectItem>
                <SelectItem value="240">4 hours</SelectItem>
                <SelectItem value="never">Never</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Data Settings Component
const DataSettings = ({ user, onSignOut }: any) => {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const exportData = async () => {
    try {
      // In a real app, you'd generate and download user data
      toast.success('Data export will be sent to your email');
    } catch (error) {
      toast.error('Failed to export data');
    }
  };

  const deleteAccount = async () => {
    try {
      // In a real app, you'd delete the user account
      toast.success('Account deletion request submitted');
      onSignOut();
    } catch (error) {
      toast.error('Failed to delete account');
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Data Export
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-600 mb-4">
            Download a copy of all your data including bookings, reviews, and profile information.
          </p>
          <Button onClick={exportData}>
            <Download className="h-4 w-4 mr-2" />
            Export My Data
          </Button>
        </CardContent>
      </Card>

      <Card className="border-red-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            Danger Zone
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-red-600">Delete Account</h4>
              <p className="text-sm text-gray-600 mb-4">
                Permanently delete your account and all associated data. This action cannot be undone.
              </p>
              
              {!showDeleteConfirm ? (
                <Button 
                  variant="destructive" 
                  onClick={() => setShowDeleteConfirm(true)}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Account
                </Button>
              ) : (
                <div className="space-y-3">
                  <p className="text-sm font-medium text-red-600">
                    Are you sure? This will permanently delete your account.
                  </p>
                  <div className="flex space-x-3">
                    <Button variant="destructive" onClick={deleteAccount}>
                      Yes, Delete My Account
                    </Button>
                    <Button variant="outline" onClick={() => setShowDeleteConfirm(false)}>
                      Cancel
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default UserSettings;
