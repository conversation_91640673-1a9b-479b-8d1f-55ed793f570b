import { useState } from 'react';
import { <PERSON>u, X, User, Shield } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { AuthModal } from '@/components/AuthModal';
import { useAuth } from '@/hooks/useAuth';
import { useAdmin } from '@/hooks/useAdmin';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const { user, signOut } = useAuth();
  const { isAdmin } = useAdmin();

  const handleAuthClick = () => {
    if (user) {
      signOut();
    } else {
      setIsAuthModalOpen(true);
    }
  };

  return (
    <>
      <header className="bg-white shadow-sm border-b sticky top-0 z-40">
        <div className="max-w-6xl mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <div className="flex items-center">
              <a href="/" className="text-2xl font-bold text-green-600">
                JaoKhelo
              </a>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center space-x-8">
              <a href="/" className="text-gray-700 hover:text-green-600 transition-colors">
                Home
              </a>
              <a href="/venues" className="text-gray-700 hover:text-green-600 transition-colors">
                Venues
              </a>
              <a href="#" className="text-gray-700 hover:text-green-600 transition-colors">
                Coaches
              </a>
              <a href="#" className="text-gray-700 hover:text-green-600 transition-colors">
                About
              </a>
              {user && isAdmin ? (
                <a href="/admin" className="text-green-600 hover:text-green-700 transition-colors flex items-center">
                  <Shield className="h-4 w-4 mr-1" />
                  Admin Dashboard
                </a>
              ) : (
                <a href="/admin/login" className="text-gray-700 hover:text-green-600 transition-colors flex items-center">
                  <Shield className="h-4 w-4 mr-1" />
                  Admin Login
                </a>
              )}
            </nav>

            {/* Auth Button */}
            <div className="hidden md:flex items-center space-x-4">
              {user ? (
                <div className="flex items-center space-x-3">
                  <a href="/dashboard" className="text-gray-700 hover:text-green-600 transition-colors">
                    Dashboard
                  </a>
                  <div className="flex items-center space-x-2">
                    <User className="h-5 w-5 text-gray-600" />
                    <span className="text-sm text-gray-700">
                      {user.user_metadata?.full_name || user.email}
                    </span>
                  </div>
                  <Button variant="outline" onClick={handleAuthClick}>
                    Sign Out
                  </Button>
                </div>
              ) : (
                <Button onClick={handleAuthClick}>
                  Sign In
                </Button>
              )}
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="text-gray-700 hover:text-green-600 transition-colors"
              >
                {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
              </button>
            </div>
          </div>

          {/* Mobile Navigation */}
          {isMenuOpen && (
            <div className="md:hidden py-4 border-t">
              <nav className="flex flex-col space-y-3">
                <a href="/" className="text-gray-700 hover:text-green-600 transition-colors py-2">
                  Home
                </a>
                <a href="/venues" className="text-gray-700 hover:text-green-600 transition-colors py-2">
                  Venues
                </a>
                <a href="#" className="text-gray-700 hover:text-green-600 transition-colors py-2">
                  Coaches
                </a>
                <a href="#" className="text-gray-700 hover:text-green-600 transition-colors py-2">
                  About
                </a>
                {user && isAdmin ? (
                  <a href="/admin" className="text-green-600 hover:text-green-700 transition-colors py-2 flex items-center">
                    <Shield className="h-4 w-4 mr-1" />
                    Admin Dashboard
                  </a>
                ) : (
                  <a href="/admin/login" className="text-gray-700 hover:text-green-600 transition-colors py-2 flex items-center">
                    <Shield className="h-4 w-4 mr-1" />
                    Admin Login
                  </a>
                )}
                <div className="pt-3 border-t">
                  {user ? (
                    <div className="space-y-2">
                      <a href="/dashboard" className="text-gray-700 hover:text-green-600 transition-colors py-2 block">
                        Dashboard
                      </a>
                      <div className="flex items-center space-x-2 py-2">
                        <User className="h-5 w-5 text-gray-600" />
                        <span className="text-sm text-gray-700">
                          {user.user_metadata?.full_name || user.email}
                        </span>
                      </div>
                      <Button variant="outline" onClick={handleAuthClick} className="w-full">
                        Sign Out
                      </Button>
                    </div>
                  ) : (
                    <Button onClick={handleAuthClick} className="w-full">
                      Sign In
                    </Button>
                  )}
                </div>
              </nav>
            </div>
          )}
        </div>
      </header>

      <AuthModal 
        isOpen={isAuthModalOpen} 
        onClose={() => setIsAuthModalOpen(false)}
        onSuccess={() => setIsAuthModalOpen(false)}
      />
    </>
  );
};

export default Header;
