import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { Navigate } from 'react-router-dom';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { 
  HelpCircle, 
  MessageSquare, 
  Phone, 
  Mail,
  Send,
  Clock,
  CheckCircle,
  AlertCircle,
  Search,
  Book,
  CreditCard,
  User,
  Settings
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { format } from 'date-fns';
import { useForm } from 'react-hook-form';

const UserSupport = () => {
  const { user, loading } = useAuth();
  const [tickets, setTickets] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  // Redirect to login if not authenticated
  if (!loading && !user) {
    return <Navigate to="/login" replace />;
  }

  // Fetch support tickets
  const fetchTickets = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      
      // Mock support tickets for now
      const mockTickets = [
        {
          id: 1,
          ticket_number: 'TKT-001',
          subject: 'Booking cancellation issue',
          category: 'booking',
          status: 'open',
          priority: 'medium',
          created_at: new Date().toISOString(),
          last_reply: new Date().toISOString(),
          messages: [
            {
              id: 1,
              message: 'I am unable to cancel my booking for tomorrow. The cancel button is not working.',
              sender: 'user',
              created_at: new Date().toISOString()
            }
          ]
        },
        {
          id: 2,
          ticket_number: 'TKT-002',
          subject: 'Payment refund request',
          category: 'payment',
          status: 'resolved',
          priority: 'high',
          created_at: new Date(Date.now() - ********).toISOString(),
          last_reply: new Date(Date.now() - 3600000).toISOString(),
          messages: [
            {
              id: 1,
              message: 'I need a refund for booking #BK123 as the venue was closed.',
              sender: 'user',
              created_at: new Date(Date.now() - ********).toISOString()
            },
            {
              id: 2,
              message: 'We have processed your refund. It will reflect in your account within 3-5 business days.',
              sender: 'support',
              created_at: new Date(Date.now() - 3600000).toISOString()
            }
          ]
        }
      ];

      setTickets(mockTickets);
    } catch (error) {
      console.error('Error fetching tickets:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchTickets();
  }, [user]);

  if (loading || isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading support center...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Help & Support</h1>
          <p className="text-gray-600">Get help with your account, bookings, and more</p>
        </div>

        {/* Quick Contact */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <Phone className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <h3 className="font-medium">Call Us</h3>
              <p className="text-sm text-gray-600">+880 1234-567890</p>
              <p className="text-xs text-gray-500">9 AM - 9 PM</p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <Mail className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <h3 className="font-medium">Email Us</h3>
              <p className="text-sm text-gray-600"><EMAIL></p>
              <p className="text-xs text-gray-500">24/7 Support</p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <MessageSquare className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <h3 className="font-medium">Live Chat</h3>
              <p className="text-sm text-gray-600">Chat with us now</p>
              <Button size="sm" className="mt-2">Start Chat</Button>
            </CardContent>
          </Card>
        </div>

        {/* Support Tabs */}
        <Tabs defaultValue="faq" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="faq">FAQ</TabsTrigger>
            <TabsTrigger value="tickets">My Tickets ({tickets.length})</TabsTrigger>
            <TabsTrigger value="new-ticket">New Ticket</TabsTrigger>
            <TabsTrigger value="guides">Guides</TabsTrigger>
          </TabsList>

          <TabsContent value="faq">
            <FAQSection searchQuery={searchQuery} onSearch={setSearchQuery} />
          </TabsContent>

          <TabsContent value="tickets">
            <TicketsSection tickets={tickets} onRefresh={fetchTickets} />
          </TabsContent>

          <TabsContent value="new-ticket">
            <NewTicketSection user={user} onTicketCreated={fetchTickets} />
          </TabsContent>

          <TabsContent value="guides">
            <GuidesSection />
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

// FAQ Section
const FAQSection = ({ searchQuery, onSearch }: any) => {
  const faqs = [
    {
      category: 'Booking',
      icon: Book,
      questions: [
        {
          question: 'How do I book a venue?',
          answer: 'You can book a venue by browsing our venues page, selecting your preferred venue, choosing date and time, and completing the payment process.'
        },
        {
          question: 'Can I cancel my booking?',
          answer: 'Yes, you can cancel your booking up to 24 hours before the scheduled time. Cancellations made within 24 hours may incur charges.'
        },
        {
          question: 'How do I reschedule my booking?',
          answer: 'Go to your bookings page, find the booking you want to reschedule, and click the reschedule option. Subject to venue availability.'
        }
      ]
    },
    {
      category: 'Payment',
      icon: CreditCard,
      questions: [
        {
          question: 'What payment methods do you accept?',
          answer: 'We accept all major credit cards, debit cards, mobile banking (bKash, Nagad, Rocket), and bank transfers.'
        },
        {
          question: 'When will I be charged?',
          answer: 'Payment is processed immediately upon booking confirmation. For some venues, a deposit may be required.'
        },
        {
          question: 'How do refunds work?',
          answer: 'Refunds are processed within 3-5 business days for eligible cancellations. The refund amount depends on the cancellation policy.'
        }
      ]
    },
    {
      category: 'Account',
      icon: User,
      questions: [
        {
          question: 'How do I update my profile?',
          answer: 'Go to your dashboard, click on Profile Settings, and update your information. Don\'t forget to save your changes.'
        },
        {
          question: 'I forgot my password',
          answer: 'Click on "Forgot Password" on the login page and follow the instructions sent to your email.'
        },
        {
          question: 'How do I delete my account?',
          answer: 'Go to Settings > Data > Danger Zone and follow the account deletion process. This action cannot be undone.'
        }
      ]
    }
  ];

  const filteredFAQs = faqs.map(category => ({
    ...category,
    questions: category.questions.filter(q =>
      q.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      q.answer.toLowerCase().includes(searchQuery.toLowerCase())
    )
  })).filter(category => category.questions.length > 0);

  return (
    <div className="space-y-6">
      {/* Search */}
      <Card>
        <CardContent className="p-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search frequently asked questions..."
              value={searchQuery}
              onChange={(e) => onSearch(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* FAQ Categories */}
      {filteredFAQs.map((category) => (
        <Card key={category.category}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <category.icon className="h-5 w-5" />
              {category.category}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Accordion type="single" collapsible>
              {category.questions.map((faq, index) => (
                <AccordionItem key={index} value={`${category.category}-${index}`}>
                  <AccordionTrigger>{faq.question}</AccordionTrigger>
                  <AccordionContent>{faq.answer}</AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

// Tickets Section
const TicketsSection = ({ tickets, onRefresh }: any) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-blue-100 text-blue-800';
      case 'in-progress': return 'bg-yellow-100 text-yellow-800';
      case 'resolved': return 'bg-green-100 text-green-800';
      case 'closed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (tickets.length === 0) {
    return (
      <Card>
        <CardContent className="p-12 text-center">
          <HelpCircle className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-medium text-gray-900 mb-2">No support tickets</h3>
          <p className="text-gray-500 mb-6">You haven't created any support tickets yet.</p>
          <Button>Create New Ticket</Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {tickets.map((ticket: any) => (
        <Card key={ticket.id} className="hover:shadow-md transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  <h3 className="font-semibold text-gray-900">{ticket.subject}</h3>
                  <Badge className={getStatusColor(ticket.status)}>
                    {ticket.status.charAt(0).toUpperCase() + ticket.status.slice(1)}
                  </Badge>
                  <Badge className={getPriorityColor(ticket.priority)}>
                    {ticket.priority.charAt(0).toUpperCase() + ticket.priority.slice(1)}
                  </Badge>
                </div>
                
                <p className="text-sm text-gray-600 mb-3">
                  Ticket #{ticket.ticket_number}
                </p>
                
                <div className="flex items-center space-x-4 text-sm text-gray-500">
                  <div className="flex items-center">
                    <Clock className="h-3 w-3 mr-1" />
                    Created {format(new Date(ticket.created_at), 'MMM dd, yyyy')}
                  </div>
                  <div className="flex items-center">
                    <MessageSquare className="h-3 w-3 mr-1" />
                    {ticket.messages.length} messages
                  </div>
                </div>
              </div>

              <Button variant="outline" size="sm">
                View Details
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

// New Ticket Section
const NewTicketSection = ({ user, onTicketCreated }: any) => {
  const { register, handleSubmit, reset, formState: { errors } } = useForm();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const onSubmit = async (data: any) => {
    setIsSubmitting(true);
    try {
      // In a real app, you'd create the ticket in the database
      toast.success('Support ticket created successfully');
      reset();
      onTicketCreated();
    } catch (error) {
      console.error('Error creating ticket:', error);
      toast.error('Failed to create support ticket');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Create New Support Ticket</CardTitle>
        <p className="text-sm text-gray-600">
          Describe your issue and we'll get back to you as soon as possible.
        </p>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {/* Category */}
          <div className="space-y-2">
            <Label htmlFor="category">Category</Label>
            <Select {...register('category', { required: 'Category is required' })}>
              <SelectTrigger>
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="booking">Booking Issues</SelectItem>
                <SelectItem value="payment">Payment & Billing</SelectItem>
                <SelectItem value="account">Account & Profile</SelectItem>
                <SelectItem value="technical">Technical Issues</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
            {errors.category && (
              <p className="text-sm text-red-600">{errors.category.message}</p>
            )}
          </div>

          {/* Priority */}
          <div className="space-y-2">
            <Label htmlFor="priority">Priority</Label>
            <Select {...register('priority', { required: 'Priority is required' })}>
              <SelectTrigger>
                <SelectValue placeholder="Select priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="low">Low - General inquiry</SelectItem>
                <SelectItem value="medium">Medium - Issue affecting usage</SelectItem>
                <SelectItem value="high">High - Urgent issue</SelectItem>
              </SelectContent>
            </Select>
            {errors.priority && (
              <p className="text-sm text-red-600">{errors.priority.message}</p>
            )}
          </div>

          {/* Subject */}
          <div className="space-y-2">
            <Label htmlFor="subject">Subject</Label>
            <Input
              id="subject"
              {...register('subject', { required: 'Subject is required' })}
              placeholder="Brief description of your issue"
            />
            {errors.subject && (
              <p className="text-sm text-red-600">{errors.subject.message}</p>
            )}
          </div>

          {/* Message */}
          <div className="space-y-2">
            <Label htmlFor="message">Message</Label>
            <Textarea
              id="message"
              {...register('message', { required: 'Message is required' })}
              placeholder="Provide detailed information about your issue..."
              rows={6}
            />
            {errors.message && (
              <p className="text-sm text-red-600">{errors.message.message}</p>
            )}
          </div>

          {/* Submit */}
          <Button type="submit" disabled={isSubmitting} className="w-full">
            {isSubmitting ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Creating Ticket...
              </div>
            ) : (
              <>
                <Send className="h-4 w-4 mr-2" />
                Create Ticket
              </>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

// Guides Section
const GuidesSection = () => {
  const guides = [
    {
      title: 'Getting Started with KhelteThako',
      description: 'Learn how to create an account and make your first booking',
      category: 'Beginner',
      readTime: '5 min read'
    },
    {
      title: 'How to Find the Perfect Venue',
      description: 'Tips for searching and filtering venues based on your needs',
      category: 'Booking',
      readTime: '3 min read'
    },
    {
      title: 'Managing Your Bookings',
      description: 'Learn how to view, modify, and cancel your bookings',
      category: 'Booking',
      readTime: '4 min read'
    },
    {
      title: 'Payment Methods and Billing',
      description: 'Understanding payment options and billing cycles',
      category: 'Payment',
      readTime: '6 min read'
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {guides.map((guide, index) => (
        <Card key={index} className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-6">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Badge variant="secondary">{guide.category}</Badge>
                <span className="text-xs text-gray-500">{guide.readTime}</span>
              </div>
              
              <h3 className="font-semibold text-gray-900">{guide.title}</h3>
              <p className="text-sm text-gray-600">{guide.description}</p>
              
              <Button variant="outline" size="sm" className="w-full">
                Read Guide
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default UserSupport;
