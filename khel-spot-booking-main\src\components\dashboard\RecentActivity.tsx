import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Clock, 
  Calendar, 
  MapPin, 
  CreditCard,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';
import { format, formatDistanceToNow } from 'date-fns';
import { Link } from 'react-router-dom';

interface Booking {
  id: number;
  booking_number: string;
  booking_date: string;
  start_time: string;
  end_time: string;
  status: string;
  payment_status: string;
  total_amount: string;
  created_at: string;
  venues?: {
    id: number;
    name: string;
    city: string;
    area: string;
  };
}

interface RecentActivityProps {
  bookings: Booking[];
}

export const RecentActivity = ({ bookings }: RecentActivityProps) => {
  const getActivityIcon = (status: string, paymentStatus: string) => {
    if (status === 'completed') {
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    }
    if (status === 'cancelled') {
      return <XCircle className="h-4 w-4 text-red-500" />;
    }
    if (paymentStatus === 'paid') {
      return <CreditCard className="h-4 w-4 text-blue-500" />;
    }
    return <AlertCircle className="h-4 w-4 text-yellow-500" />;
  };

  const getActivityMessage = (booking: Booking) => {
    const venueName = booking.venues?.name || 'Unknown Venue';
    const bookingDate = format(new Date(booking.booking_date), 'MMM dd');
    
    if (booking.status === 'completed') {
      return `Completed booking at ${venueName} on ${bookingDate}`;
    }
    if (booking.status === 'cancelled') {
      return `Cancelled booking at ${venueName} for ${bookingDate}`;
    }
    if (booking.payment_status === 'paid') {
      return `Payment confirmed for ${venueName} booking on ${bookingDate}`;
    }
    if (booking.status === 'confirmed') {
      return `Booking confirmed at ${venueName} for ${bookingDate}`;
    }
    return `New booking created at ${venueName} for ${bookingDate}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (bookings.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Recent Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No recent activity</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Recent Activity
          </div>
          <Button variant="outline" size="sm" asChild>
            <Link to="/dashboard/activity">View All</Link>
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {bookings.map((booking) => (
            <div
              key={booking.id}
              className="flex items-start space-x-4 p-3 rounded-lg hover:bg-gray-50 transition-colors"
            >
              {/* Activity Icon */}
              <div className="flex-shrink-0 mt-1">
                {getActivityIcon(booking.status, booking.payment_status)}
              </div>

              {/* Activity Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <p className="text-sm text-gray-900">
                      {getActivityMessage(booking)}
                    </p>
                    
                    <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                      <div className="flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        {format(new Date(booking.booking_date), 'MMM dd, yyyy')}
                      </div>
                      
                      <div className="flex items-center">
                        <MapPin className="h-3 w-3 mr-1" />
                        {booking.venues?.area}, {booking.venues?.city}
                      </div>
                      
                      <div className="font-medium text-green-600">
                        ৳{parseFloat(booking.total_amount).toLocaleString()}
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col items-end space-y-2">
                    <Badge className={getStatusColor(booking.status)}>
                      {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                    </Badge>
                    
                    <span className="text-xs text-gray-400">
                      {formatDistanceToNow(new Date(booking.created_at), { addSuffix: true })}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* View All Link */}
        <div className="mt-6 text-center">
          <Button variant="outline" asChild>
            <Link to="/dashboard/bookings">
              View All Bookings
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
