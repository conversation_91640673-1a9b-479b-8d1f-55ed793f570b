import { useState, useEffect, createContext, useContext } from 'react';
import { User } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';

interface AdminContextType {
  isAdmin: boolean;
  loading: boolean;
}

const AdminContext = createContext<AdminContextType>({
  isAdmin: false,
  loading: true,
});

export const AdminProvider = ({ children }: { children: React.ReactNode }) => {
  const { user, loading: authLoading } = useAuth();
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkAdminStatus = async () => {
      // CRITICAL: Wait for auth to finish loading before doing anything
      // This prevents database queries when auth state is indeterminate
      if (authLoading) {
        console.log('Auth still loading, waiting...');
        return;
      }

      // If auth has finished loading and user is null, set not admin and stop loading
      if (!user) {
        console.log('Auth finished loading, no user found');
        setIsAdmin(false);
        setLoading(false);
        return;
      }

      try {
        console.log('Checking admin status for authenticated user:', user.email, user.id);
        
        // Primary check: hardcoded admin email (immediate admin access)
        if (user.email === '<EMAIL>') {
          console.log('Admin access granted via email check');
          setIsAdmin(true);
          setLoading(false);
          return;
        }

        // Secondary check: hardcoded admin UUID (immediate admin access)
        const adminUuid = '4caf64e5-9b92-48e7-a49c-593b8248f489';
        if (user.id === adminUuid) {
          console.log('Admin access granted via ID check');
          setIsAdmin(true);
          setLoading(false);
          return;
        }

        // Only attempt database query if user is authenticated AND not the known admin
        // This prevents RLS policy violations
        try {
          const { data, error } = await supabase
            .from('users')
            .select('is_admin')
            .eq('id', user.id)
            .single();

          if (error) {
            console.warn('Database admin check failed:', error.message);
            setIsAdmin(false);
          } else {
            setIsAdmin(Boolean(data?.is_admin));
          }
        } catch (dbError) {
          console.warn('Database query error:', dbError);
          setIsAdmin(false);
        }
      } catch (error) {
        console.error('Error in admin status check:', error);
        setIsAdmin(false);
      } finally {
        setLoading(false);
      }
    };

    checkAdminStatus();
  }, [user, authLoading]);

  return (
    <AdminContext.Provider value={{ isAdmin, loading }}>
      {children}
    </AdminContext.Provider>
  );
};

export const useAdmin = () => {
  const context = useContext(AdminContext);
  if (!context) {
    throw new Error('useAdmin must be used within an AdminProvider');
  }
  return context;
};
